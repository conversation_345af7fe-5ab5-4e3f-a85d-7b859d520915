# 查询性能参数配置指南

本文档详细列出了项目中所有影响查询效果的关键性能参数，包括参数位置、作用说明和调优建议。

## 📋 参数分类概览

### 1. 检索相关参数

- **similarity_top_k**: 最终返回结果数量，也是给大语言模型的文本块的数量
- **sparse_top_k**: 稀疏向量检索候选数量
- **混合检索权重**: BM25 和向量检索的权重配置
- **相似度阈值**: 结果过滤阈值

### 2. 文本处理参数

- **chunk_size**: 文本切分块大小
- **chunk_overlap**: 文本块重叠字符数
- **separator**: 文本分隔符配置

### 3. 模型配置参数

- **temperature**: LLM 生成温度
- **embedding_model**: 嵌入模型选择
- **dimensions**: 向量维度配置

### 4. 系统性能参数

- **batch_size**: 批处理大小
- **alpha**: Qdrant 混合检索权重
- **grpc 配置**: 连接性能优化

---

## 🔍 详细参数配置

### 1. 检索核心参数

#### 1.1 similarity_top_k (最终返回结果数量)

**位置**:

- `backend/config/settings.py:43`
- `backend/app/models/requests.py:11`
- `backend/app/services/rag_service.py:217,632`

**当前值**: `5`

**作用**: 控制查询引擎最终返回给用户的文档片段数量

**调优建议**:

- 一般场景: 3-5 个结果
- 复杂查询: 5-10 个结果
- 简单查询: 1-3 个结果
- 最大限制: 20 个 (API 限制)

#### 1.2 sparse_top_k (稀疏向量检索候选数量)

**位置**:

- `backend/config/settings.py:47`
- `backend/app/services/rag_service.py:217`

**当前值**: `10`

**作用**: BM25 稀疏向量检索阶段的候选文档数量，用于后续与密集向量结果融合

**调优建议**:

- 默认值: 10 (推荐)
- 高精度需求: 15-20
- 性能优先: 5-8
- 注意: 该值应大于等于 similarity_top_k

#### 1.3 混合检索权重配置

**位置**:

- `backend/config/settings.py:41-42`
- `backend/app/models/requests.py:15-26`
- `backend/app/services/rag_service.py:610-611`

**当前值**:

```python
default_bm25_weight: float = 0.5    # BM25权重
default_vector_weight: float = 0.5  # 向量权重
```

**作用**: 控制 BM25 关键词检索和语义向量检索的融合比例

**调优建议**:

- 平衡模式: `bm25=0.5, vector=0.5` (默认)
- 关键词优先: `bm25=0.7, vector=0.3` (精确匹配场景)
- 语义优先: `bm25=0.3, vector=0.7` (概念理解场景)
- 纯 BM25: `bm25=1.0, vector=0.0`
- 纯向量: `bm25=0.0, vector=1.0`

#### 1.4 相似度阈值

**位置**:

- `backend/app/models/requests.py:12`

**当前值**: `0.7`

**作用**: 过滤低相似度结果的阈值

**调优建议**:

- 严格过滤: 0.8-0.9
- 平衡模式: 0.6-0.7 (推荐)
- 宽松模式: 0.4-0.6
- 范围: 0.0-1.0

### 2. 文本处理参数

#### 2.1 chunk_size (文本块大小)

**位置**:

- `backend/config/rag_optimization.py:10`
- `docs/项目经验.md:173-175`

**当前值**: `768` (优化配置) / `512` (文档建议)

**作用**: 控制文档切分成文本块的字符数量

**调优建议**:

- 中文文档: 512-768 字符 (推荐)
- 英文文档: 1024 字符
- 短文档: 256-512 字符
- 长文档: 768-1024 字符
- 影响: 块大小影响检索精度和上下文完整性

#### 2.2 chunk_overlap (文本块重叠)

**位置**:

- `backend/config/rag_optimization.py:11`
- `docs/项目经验.md:175`

**当前值**: `30` (优化配置) / `50` (文档建议)

**作用**: 相邻文本块之间的重叠字符数，保证上下文连续性

**调优建议**:

- 标准配置: 30-50 字符
- 高连续性需求: 50-100 字符
- 性能优先: 10-30 字符
- 比例建议: chunk_overlap = chunk_size \* 0.1-0.2

#### 2.3 separator (文本分隔符)

**位置**:

- `backend/config/rag_optimization.py:12`

**当前值**: `"\n\n"` (段落分隔符)

**作用**: 文本切分时的优先分隔符

**调优建议**:

- 段落分隔: `"\n\n"` (推荐)
- 句子分隔: `"。"`
- 自然分隔: `["\n\n", "\n", "。", "！", "？"]`

### 3. 模型配置参数

#### 3.1 temperature (LLM 生成温度)

**位置**:

- `backend/app/services/rag_service.py:64`

**当前值**: `0.1`

**作用**: 控制 LLM 回答的随机性和创造性

**调优建议**:

- 事实性回答: 0.0-0.2 (推荐)
- 平衡模式: 0.3-0.5
- 创造性回答: 0.6-0.8
- 高创造性: 0.8-1.0

#### 3.2 embedding_model (嵌入模型)

**位置**:

- `backend/config/settings.py:27`
- `backend/app/services/rag_service.py:71`

**当前值**: `"text-embedding-3-small"`

**作用**: 文档和查询的向量化模型

**调优建议**:

- 性价比: `text-embedding-3-small` (1536 维)
- 高精度: `text-embedding-3-large` (3072 维)
- 兼容性: `text-embedding-ada-002` (1536 维)

#### 3.3 dimensions (向量维度)

**位置**:

- `backend/app/services/rag_service.py:72`
- `init_qdrant_collection.py:44`

**当前值**: `1536`

**作用**: 向量嵌入的维度数量

**调优建议**:

- 标准配置: 1536 维 (推荐)
- 高精度需求: 3072 维 (需要更多资源)
- 注意: 必须与嵌入模型维度匹配

### 4. 系统性能参数

#### 4.1 batch_size (批处理大小)

**位置**:

- `backend/app/services/rag_service.py:106`

**当前值**: `20`

**作用**: 稀疏向量生成的批处理大小

**调优建议**:

- 标准配置: 20 (推荐)
- 高性能服务器: 50-100
- 低配置环境: 10-20
- 影响: 批处理大小影响内存使用和处理速度

#### 4.2 alpha (Qdrant 混合检索权重)

**位置**:

- `backend/config/settings.py:48`
- `backend/app/services/rag_service.py:213,219`

**当前值**: `0.5`

**作用**: Qdrant 内部密集向量和稀疏向量的融合权重

**调优建议**:

- 平衡模式: 0.5 (推荐)
- 密集向量优先: 0.6-0.8
- 稀疏向量优先: 0.2-0.4
- 注意: 这是 Qdrant 内部参数，与 API 层面的权重不同

#### 4.3 GRPC 配置

**位置**:

- `backend/config/settings.py:38`

**当前值**: `qdrant_prefer_grpc: bool = True`

**作用**: 是否优先使用 GRPC 连接 Qdrant

**调优建议**:

- 本地部署: True (推荐，性能更好)
- 远程连接: False (HTTP 更稳定)
- 网络环境差: False

---

## 🎯 场景化调优建议

### 精确匹配场景

```python
# 适用于：法律文档、技术规范等需要精确匹配的场景
similarity_top_k = 3
sparse_top_k = 8
bm25_weight = 0.7
vector_weight = 0.3
similarity_threshold = 0.8
chunk_size = 512
chunk_overlap = 30
temperature = 0.0
```

### 语义理解场景

```python
# 适用于：概念解释、知识问答等需要语义理解的场景
similarity_top_k = 5
sparse_top_k = 10
bm25_weight = 0.3
vector_weight = 0.7
similarity_threshold = 0.6
chunk_size = 768
chunk_overlap = 50
temperature = 0.1
```

### 高性能场景

```python
# 适用于：高并发、快速响应的场景
similarity_top_k = 3
sparse_top_k = 6
bm25_weight = 0.5
vector_weight = 0.5
similarity_threshold = 0.7
chunk_size = 512
chunk_overlap = 20
batch_size = 50
```

### 高精度场景

```python
# 适用于：研究分析、深度问答的场景
similarity_top_k = 8
sparse_top_k = 15
bm25_weight = 0.4
vector_weight = 0.6
similarity_threshold = 0.5
chunk_size = 1024
chunk_overlap = 100
temperature = 0.2
```

---

## ⚠️ 调优注意事项

1. **参数关联性**:

   - `sparse_top_k >= similarity_top_k`
   - `bm25_weight + vector_weight = 1.0`
   - `chunk_overlap < chunk_size`

2. **性能影响**:

   - 增大`similarity_top_k`和`sparse_top_k`会增加计算开销
   - 增大`chunk_size`会增加内存使用
   - 减小`chunk_overlap`可能影响上下文连续性

3. **质量平衡**:

   - 过高的`similarity_threshold`可能导致无结果
   - 过低的`temperature`可能导致回答过于机械
   - 过大的`chunk_size`可能导致信息冗余

4. **环境适配**:
   - 根据硬件配置调整`batch_size`
   - 根据网络环境选择 GRPC 或 HTTP
   - 根据数据特点调整文本处理参数

---

## 📊 性能监控指标

建议监控以下指标来评估参数调优效果：

1. **响应时间**: `processing_time`字段
2. **检索精度**: 用户反馈和人工评估
3. **资源使用**: 内存、CPU 使用率
4. **命中率**: 查询返回结果的比例
5. **用户满意度**: 基于实际使用反馈

通过持续监控这些指标，可以不断优化参数配置，提升系统整体性能。
