#!/usr/bin/env python3
"""
安装Qdrant混合检索所需的依赖
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"📦 安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✅ {package} 已安装")
        return True
    except ImportError:
        print(f"❌ {package} 未安装")
        return False

def main():
    print("🚀 检查和安装Qdrant混合检索依赖")
    print("=" * 50)
    
    # 需要的包
    required_packages = [
        ("fastembed", "fastembed>=0.3.0"),
        ("qdrant_client", "qdrant-client>=1.7.0"),
    ]
    
    all_installed = True
    
    for import_name, pip_name in required_packages:
        if not check_package(import_name):
            if not install_package(pip_name):
                all_installed = False
    
    print("\n" + "=" * 50)
    if all_installed:
        print("🎉 所有依赖已安装完成！")
        print("\n📋 下一步:")
        print("1. 运行 python quick_test_hybrid.py 进行快速测试")
        print("2. 运行 python test_qdrant_hybrid_migration.py 进行完整测试")
    else:
        print("💥 部分依赖安装失败，请手动安装")
    
    return all_installed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
