#!/usr/bin/env python3
"""
清空Qdrant集合数据脚本
用于清空documents集合中的所有数据，以便重新应用新的RAG优化参数

使用方法:
python scripts/clear_qdrant_collection.py

注意: 此操作不可逆，请确保已备份重要数据
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import qdrant_client
from qdrant_client.http.exceptions import UnexpectedResponse
from backend.config.settings import settings
from backend.config.logging_config import get_logger

logger = get_logger(__name__)


def clear_qdrant_collection():
    """清空Qdrant集合中的所有数据"""
    try:
        print("🔧 连接到Qdrant服务器...")
        
        # 创建Qdrant客户端
        client = qdrant_client.QdrantClient(
            host=settings.qdrant_host,
            port=settings.qdrant_port,
            api_key=settings.qdrant_api_key if settings.qdrant_api_key else None,
            prefer_grpc=settings.qdrant_prefer_grpc
        )
        
        collection_name = settings.collection_name
        print(f"📋 目标集合: {collection_name}")
        
        # 检查集合是否存在
        try:
            collection_info = client.get_collection(collection_name)
            print(f"✅ 找到集合: {collection_name}")
            print(f"📊 当前点数量: {collection_info.points_count}")
            
            if collection_info.points_count == 0:
                print("ℹ️  集合已经是空的，无需清理")
                return True
                
        except UnexpectedResponse as e:
            if "Not found" in str(e):
                print(f"ℹ️  集合 {collection_name} 不存在，无需清理")
                return True
            else:
                raise e
        
        # 确认操作
        print(f"\n⚠️  警告: 即将删除集合 '{collection_name}' 中的所有数据!")
        print("📝 这将清除:")
        print("   - 所有文档向量数据")
        print("   - 所有稀疏向量数据(BM25)")
        print("   - 所有元数据信息")
        print("   - 此操作不可逆!")
        
        confirm = input("\n❓ 确认要继续吗? (输入 'yes' 确认): ").strip().lower()
        
        if confirm != 'yes':
            print("❌ 操作已取消")
            return False
        
        print("\n🗑️  开始清空集合数据...")
        
        # 方法1: 删除所有点 (推荐，保留集合结构)
        try:
            # 获取所有点的ID
            scroll_result = client.scroll(
                collection_name=collection_name,
                limit=10000,  # 一次获取最多10000个点
                with_payload=False,
                with_vectors=False
            )
            
            all_point_ids = []
            points, next_page_offset = scroll_result
            
            while points:
                point_ids = [point.id for point in points]
                all_point_ids.extend(point_ids)
                print(f"📋 已收集 {len(all_point_ids)} 个点ID...")
                
                if next_page_offset is None:
                    break
                    
                # 获取下一页
                scroll_result = client.scroll(
                    collection_name=collection_name,
                    offset=next_page_offset,
                    limit=10000,
                    with_payload=False,
                    with_vectors=False
                )
                points, next_page_offset = scroll_result
            
            if all_point_ids:
                print(f"🗑️  删除 {len(all_point_ids)} 个数据点...")
                
                # 分批删除 (每批1000个)
                batch_size = 1000
                for i in range(0, len(all_point_ids), batch_size):
                    batch_ids = all_point_ids[i:i + batch_size]
                    client.delete(
                        collection_name=collection_name,
                        points_selector=batch_ids
                    )
                    print(f"✅ 已删除 {min(i + batch_size, len(all_point_ids))}/{len(all_point_ids)} 个点")
                
                print("✅ 所有数据点删除完成")
            else:
                print("ℹ️  集合中没有数据点")
                
        except Exception as e:
            print(f"⚠️  批量删除失败，尝试重新创建集合: {e}")
            
            # 方法2: 删除并重新创建集合 (备用方案)
            print("🔄 删除整个集合...")
            client.delete_collection(collection_name)
            print("✅ 集合删除完成")
            
            print("🔧 重新创建集合...")
            from qdrant_client.http import models
            
            client.create_collection(
                collection_name=collection_name,
                vectors_config={
                    "text-dense": models.VectorParams(
                        size=1536,  # OpenAI text-embedding-3-small 的向量维度
                        distance=models.Distance.COSINE,
                    )
                },
                sparse_vectors_config={
                    "text-sparse": models.SparseVectorParams(
                        index=models.SparseIndexParams()
                    )
                },
            )
            print("✅ 集合重新创建完成")
        
        # 验证清理结果
        final_info = client.get_collection(collection_name)
        print(f"\n📊 清理后状态:")
        print(f"   - 集合名称: {collection_name}")
        print(f"   - 点数量: {final_info.points_count}")
        print(f"   - 向量配置: 密集向量(1536维) + 稀疏向量")
        
        if final_info.points_count == 0:
            print("✅ 集合清理成功!")
            print("\n📝 下一步操作:")
            print("   1. 重新启动应用以应用新的RAG优化参数")
            print("   2. 重新上传文档到data目录")
            print("   3. 点击'加载文档'按钮重新处理文档")
            return True
        else:
            print(f"⚠️  警告: 集合中仍有 {final_info.points_count} 个点")
            return False
            
    except Exception as e:
        logger.error(f"清理Qdrant集合失败: {e}")
        print(f"❌ 清理失败: {e}")
        return False


def main():
    """主函数"""
    print("🧹 Qdrant集合清理工具")
    print("=" * 50)
    
    try:
        # 检查Qdrant连接
        print("🔍 检查Qdrant连接...")
        client = qdrant_client.QdrantClient(
            host=settings.qdrant_host,
            port=settings.qdrant_port,
            api_key=settings.qdrant_api_key if settings.qdrant_api_key else None,
            prefer_grpc=settings.qdrant_prefer_grpc
        )
        
        # 测试连接
        collections = client.get_collections()
        print(f"✅ Qdrant连接成功 ({settings.qdrant_host}:{settings.qdrant_port})")
        print(f"📋 当前集合数量: {len(collections.collections)}")
        
        # 执行清理
        success = clear_qdrant_collection()
        
        if success:
            print("\n🎉 清理操作完成!")
            print("💡 提示: 现在可以重新启动应用并重新加载文档了")
        else:
            print("\n❌ 清理操作失败!")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        print("\n🔧 请检查:")
        print("   1. Qdrant服务是否正在运行")
        print("   2. 连接配置是否正确")
        print("   3. 网络连接是否正常")
        sys.exit(1)


if __name__ == "__main__":
    main()
