#!/usr/bin/env python3
"""
快速测试Qdrant混合检索功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

def test_hybrid_search():
    """快速测试混合检索"""
    try:
        from backend.app.services.rag_service import RAGService
        
        print("🔧 初始化RAGService...")
        rag_service = RAGService()
        
        print("📋 检查Qdrant集合配置...")
        if not rag_service._validate_bm25_readiness():
            print("❌ Qdrant混合检索配置不正确")
            return False
        
        print("✅ Qdrant混合检索配置正确")
        
        # 测试查询
        print("🔍 测试混合检索查询...")
        result = rag_service.query(
            question="人工智能技术",
            bm25_weight=0.5,
            vector_weight=0.5,
            max_results=3
        )
        
        if result["success"]:
            print(f"✅ 混合检索成功: {len(result['sources'])} 个结果")
            print(f"📊 检索模式: {result.get('retrieval_mode', 'unknown')}")
            return True
        else:
            print(f"❌ 混合检索失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 快速测试Qdrant混合检索")
    print("=" * 40)
    
    success = test_hybrid_search()
    
    if success:
        print("🎉 混合检索功能正常！")
    else:
        print("💥 混合检索功能异常，请检查配置")
    
    sys.exit(0 if success else 1)
