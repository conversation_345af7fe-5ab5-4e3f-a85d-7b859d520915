# 项目经验总结

## 重要人类经验：禁止 AI 修改

**后端服务管理**

- 关闭后端要优雅，ctrl+c 慢慢等
- netstat -ano | findstr :8000
- 在 windows 本地用 cmd 执行 python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
- 别用 powershell，容易出现进程残留

**一些心得**

- context 不能太全面，在项目后期，可能这个 context engineering 就是自欺欺人。文档多得人和 ai 都记不住，难以维护。但是不维护又会让 ai 的表现下降。这是噩梦。
- 牢记 embedding_metadata 表的\_node_content 键的值是一个 json 文件，json 就相当于一个字典。唯一的问题是，字典中的值都是 unicode。
- 每次要添加新的功能，要先查清楚资料，然后先用 jupyter notebook 把关键代码实现之后看效果
