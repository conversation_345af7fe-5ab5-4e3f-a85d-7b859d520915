# Qdrant 数据字典

本文档详细说明了 Qdrant 向量数据库中每个 point（数据点）的字段结构和作用。

## 概述

每个 Qdrant point 代表一个文档的文本块（chunk），包含元数据、向量嵌入和 LlamaIndex 节点信息。

## 顶层字段 (Top-level Fields)

### 文件基础信息
| 字段名 | 类型 | 作用 | 示例 |
|--------|------|------|------|
| `file_url` | string | 文档的原始URL地址，用于生成可点击的引用链接 | `https://www.gzmdrw.cn/...` |
| `file_type` | string | 文件MIME类型，标识文档格式 | `text/plain` |
| `file_name` | string | 文件名称，用于显示和管理 | `310000000000337.txt` |
| `file_path` | string | 文件在本地存储的路径 | `data\\310000000000337.txt` |
| `file_size` | integer | 文件大小（字节），用于存储管理 | `14953` |
| `filename` | string | 文件名的别名字段 | `310000000000337.txt` |

### 文档标识信息
| 字段名 | 类型 | 作用 | 示例 |
|--------|------|------|------|
| `document_id` | string | 文档的唯一标识符 | UUID格式 |
| `doc_id` | string | 文档ID的别名字段 | UUID格式 |
| `content_id` | string | CMS系统中的内容ID，用于关联原始数据 | `310000000000337` |
| `ref_doc_id` | string | 引用文档ID，用于建立文档关系 | UUID格式 |

### 时间信息
| 字段名 | 类型 | 作用 | 示例 |
|--------|------|------|------|
| `creation_date` | string | 文档创建日期 | `2025-07-10` |
| `last_modified_date` | string | 文档最后修改日期 | `2025-07-10` |
| `publish_date` | string | 文档发布日期（来自CMS） | `2015-01-05 09:26:08` |

### 内容信息
| 字段名 | 类型 | 作用 | 示例 |
|--------|------|------|------|
| `title` | string | 文档标题，用于显示和搜索 | `贵州民族大学人文科技学院2015年工作要点` |
| `source` | string | 数据来源标识 | `chestnut_cms` |
| `_node_type` | string | LlamaIndex节点类型 | `TextNode` |

## 向量字段 (Vectors)

### text-dense
- **类型**: 密集向量 (Dense Vector)
- **维度**: 1536
- **用途**: 语义相似性搜索
- **生成模型**: text-embedding-3-small

### text-sparse  
- **类型**: 稀疏向量 (Sparse Vector)
- **用途**: BM25关键词搜索
- **生成模型**: Qdrant/bm25

## _node_content 字段详解

`_node_content` 字段存储完整的 LlamaIndex TextNode 对象，包含以下结构：

### 核心字段
| 字段名 | 类型 | 作用 |
|--------|------|------|
| `id_` | string | 节点的唯一标识符 (UUID) |
| `text` | string | 文本块的实际内容，用于RAG检索和生成 |
| `embedding` | null | 嵌入向量（存储在向量字段中） |
| `mimetype` | string | 内容的MIME类型 |
| `class_name` | string | LlamaIndex类名，固定为 "TextNode" |

### 文本定位
| 字段名 | 类型 | 作用 |
|--------|------|------|
| `start_char_idx` | integer | 文本块在原文档中的起始字符位置 |
| `end_char_idx` | integer | 文本块在原文档中的结束字符位置 |

### 元数据配置
| 字段名 | 类型 | 作用 |
|--------|------|------|
| `metadata` | object | 包含所有文档元数据的对象 |
| `excluded_embed_metadata_keys` | array | 生成嵌入时排除的元数据键 |
| `excluded_llm_metadata_keys` | array | LLM处理时排除的元数据键 |
| `metadata_template` | string | 元数据格式化模板 |
| `metadata_separator` | string | 元数据分隔符 |
| `text_template` | string | 文本格式化模板 |

### 关系信息
| 字段名 | 类型 | 作用 |
|--------|------|------|
| `relationships` | object | 节点间的关系映射，用于文档结构导航 |

#### relationships 结构说明
```json
{
  "1": {
    "node_id": "父节点ID",
    "node_type": "4",  // 关系类型：4=SOURCE
    "metadata": {},    // 关联节点的元数据
    "hash": "内容哈希值",
    "class_name": "RelatedNodeInfo"
  }
}
```

## 数据流程

1. **文档上传**: CMS文章 → HTML清理 → 文本分块
2. **向量化**: 文本块 → 密集向量 + 稀疏向量
3. **存储**: Point = 元数据 + 向量 + LlamaIndex节点
4. **检索**: 混合搜索（BM25 + 语义） → 相关文本块
5. **生成**: 文本块 + 元数据 → RAG回答

## 使用场景

- **语义搜索**: 使用 text-dense 向量进行相似性匹配
- **关键词搜索**: 使用 text-sparse 向量进行精确匹配  
- **混合检索**: 结合两种向量的优势
- **引用生成**: 使用 file_url 和 title 生成可点击的引用链接
- **内容管理**: 使用 content_id 关联CMS原始数据
- **文档导航**: 使用 relationships 在相关文档间导航

## 注意事项

1. **编码一致性**: title字段存储原始中文，_node_content中可能包含Unicode转义
2. **向量维度**: 确保嵌入模型输出维度与Qdrant集合配置一致
3. **元数据过滤**: excluded_*_metadata_keys 控制哪些字段参与嵌入和LLM处理
4. **关系完整性**: relationships 字段维护文档块之间的层次结构
