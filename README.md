# RAG 聊天应用

一个基于 LlamaIndex 和 ChromaDB 的极简 RAG（检索增强生成）聊天应用。

## 🌟 功能特点

- 🔍 **智能检索**: 结合 BM25 关键词检索和向量相似度检索
- 💬 **自然对话**: 基于 GPT-4o-mini 的智能问答
- 📁 **简单易用**: 只需将 TXT 文件放入 data 目录即可
- 🌐 **Web 界面**: 美观的单页面聊天界面
- ⚡ **快速部署**: 一键启动，无需复杂配置
- 🗄️ **SQLite 存储**: 基于 ChromaDB 的 SQLite 底层存储
- 🔄 **文件替换**: 支持同名文件完全替换机制
- 📱 **响应式设计**: 支持桌面和移动设备
- 🗃️ **MySQL 支持**: 可连接外部 MySQL 数据库进行数据获取

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- OpenAI API 密钥（支持代理）

### 2. 安装和配置

```bash
# 克隆或下载项目
git clone <your-repo-url>
cd fast-gzmdrw-chat

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 创建环境配置文件
cp .env.example .env
# 编辑 .env 文件，填入你的 OpenAI API 配置
```

### 3. 配置 .env 文件

创建 `.env` 文件并配置以下参数：

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://your-proxy-url.com/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=8000
DATA_DIR=./data
STORAGE_DIR=./storage
COLLECTION_NAME=documents

# ChromaDB SQLite配置
CHROMA_DB_IMPL=duckdb+parquet
CHROMA_PERSIST_DIRECTORY=./storage

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# MySQL数据库配置（可选）
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=chestnut_cms
```

### 4. 一键启动

```bash
# 一键启动（推荐）
python start.py
```

启动脚本会自动：

- 检查 Python 版本和虚拟环境
- 安装依赖包
- 检查配置文件
- 创建必要目录
- 启动服务器
- 自动打开浏览器

### 5. 手动启动（可选）

```bash
# 安装依赖
pip install -r requirements.txt

# 启动后端服务
cd backend
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 6. 使用方法

1. 将 TXT 文档放入 `data/` 目录
2. 访问 http://localhost:8000
3. 点击"重新加载文档"按钮
4. 开始与文档对话！

## 📁 项目结构

```
fast-gzmdrw-chat/
├── backend/                 # 后端代码
│   ├── app/                # FastAPI应用
│   │   ├── __init__.py
│   │   ├── main.py         # 主应用文件
│   │   ├── dependencies.py # 依赖注入
│   │   ├── api/            # API路由层
│   │   │   └── v1/         # API v1版本
│   │   │       ├── query.py           # 查询接口
│   │   │       ├── documents.py       # 文档管理接口
│   │   │       ├── health.py          # 健康检查接口
│   │   │       └── cms_article_sync.py # CMS文章同步接口
│   │   ├── core/           # 核心配置
│   │   │   ├── events.py   # 应用事件
│   │   │   ├── middleware.py # 中间件
│   │   │   └── exceptions.py # 异常处理
│   │   ├── models/         # 数据模型
│   │   │   ├── requests.py # 请求模型
│   │   │   ├── responses.py # 响应模型
│   │   │   └── domain.py   # 领域模型
│   │   ├── services/       # 业务逻辑层 ⭐
│   │   │   ├── __init__.py
│   │   │   ├── rag_service.py        # RAG服务（ChromaDB+LlamaIndex）
│   │   │   ├── mysql_service.py      # MySQL数据服务
│   │   │   └── chestnut_cms_service.py # CMS同步协调服务
│   │   └── utils/          # 工具函数
│   ├── config/             # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py     # 配置类
│   │   └── rag_optimization.py # RAG优化配置
│   └── __init__.py
├── frontend/               # 前端代码
│   ├── static/            # 静态资源
│   │   ├── css/
│   │   │   └── style.css  # 样式文件
│   │   └── js/
│   │       ├── app.js     # 主应用逻辑
│   │       └── documents.js # 文档管理逻辑
│   └── templates/
│       ├── index.html     # 聊天页面
│       └── documents.html # 文档管理页面
├── scripts/               # 诊断和维护脚本
│   ├── README.md         # 脚本使用指南
│   ├── check_database.py # 数据库检查
│   ├── rebuild_database.py # 数据库重建
│   ├── fast_reset_database.py # 快速重置数据库
│   ├── diagnose_hnsw_error.py # HNSW错误诊断
│   ├── check_embedding_dimensions.py # 向量维度检查
│   ├── detailed_metadata_check.py # 元数据详细检查
│   └── test_document_management.py # 文档功能测试
├── docs/                  # 项目文档
│   ├── prd.md            # 产品需求文档
│   ├── chromadb-storage-analysis.md # 存储架构分析
│   ├── 项目经验.md        # 项目开发经验总结
│   └── CORS实现指南.md    # CORS配置指南
├── data/                  # 文档目录（放置TXT文件）
│   └── sample_document.txt # 示例文档
├── storage/               # ChromaDB数据存储目录
│   ├── chroma.sqlite3     # SQLite数据库文件（自动生成）
│   └── [向量数据文件]      # 向量嵌入数据（自动生成）
├── .env                  # 环境配置
├── .env.example          # 环境配置示例
├── .env.backup           # 环境配置备份
├── requirements.txt      # 依赖包
├── start.py              # 一键启动脚本
├── test_mysql_connection.py # MySQL连接测试
├── CLAUDE.md             # Claude Code 指导文档
└── README.md            # 项目说明
```

## 🔧 API 接口

### 页面路由

- `GET /` - 聊天页面（返回 HTML）

### 系统状态接口

- `GET /api/v1/status` - 获取系统状态

### 文档管理接口

- `POST /api/v1/documents/load` - 重新加载文档
- `GET /api/v1/documents/list` - 获取文档列表
- `POST /api/v1/documents/upload` - 上传单个文档
- `DELETE /api/v1/documents/{filename}` - 删除指定文档

### 查询问答接口

- `POST /api/v1/query` - 查询问答

详细的 API 文档请参考 [PRD 文档](project-management/prd.md)。

## 🛠️ 技术栈

- **后端**: FastAPI + LlamaIndex + ChromaDB
- **数据库**: SQLite（ChromaDB 底层存储，自动管理）+ MySQL（可选外部数据源）
- **前端**: HTML + TailwindCSS + Vanilla JavaScript
- **LLM**: GPT-4o-mini
- **向量模型**: text-embedding-3-small
- **数据库连接**: PyMySQL

## 💾 数据存储设计

### 核心特性

1. **无用户系统**: 单用户使用，无需认证
2. **对话历史前端存储**: 存储在浏览器 localStorage 中
3. **文档向量化存储**: 使用 ChromaDB 存储文档向量和元数据
4. **文件系统存储**: 原始 TXT 文件直接存储在 data 目录

### 数据表设计

基于 LlamaIndex 最佳实践，采用简化的单一存储架构：

#### ChromaDB Collection (documents)

- **Collection Name**: documents
- **Purpose**: 存储文档块的向量嵌入和文本内容
- **Schema**:
  - id: 文档块唯一标识
  - content: 文档块文本内容
  - metadata: 文件信息和元数据
  - embedding: 768 维向量（text-embedding-3-small）

#### 文件替换机制

**核心原则**: 文件名唯一性，同名文件完全替换

1. **检测同名文件**: 以文件名作为唯一标识
2. **完全删除旧数据**: 删除 ChromaDB 中所有相关记录和向量
3. **重新处理新文件**: 完整的文本分块、向量化、存储流程

## 🔍 混合检索

应用采用 LlamaIndex 内置的混合检索机制：

1. **BM25 检索**: 关键词匹配检索
2. **向量检索**: 语义相似度检索
3. **结果融合**: 使用 QueryFusionRetriever 融合结果
4. **重排序**: 基于相似度分数重新排序

## 📝 使用说明

### 添加文档

1. 将 TXT 文件放入 `data/` 目录
2. 点击"加载文档"按钮
3. 等待处理完成

### 文档更新

- 同名文件会完全替换旧文件的所有数据
- 新文件会自动添加到知识库中
- 删除的文件需要手动清理数据库

### 对话技巧

- 提问要具体明确
- 可以询问文档中的具体内容
- 支持多轮对话和上下文理解

## ❓ 常见问题

**Q: 如何添加新文档？**
A: 将 TXT 文件放入 data 目录，然后点击"重新加载文档"按钮。

**Q: 如果上传同名文件会怎样？**
A: 系统会自动删除旧文件的所有相关数据，然后重新处理新文件。

**Q: 支持哪些文档格式？**
A: 目前只支持 TXT 格式，后续可扩展支持 PDF、Word 等。

**Q: 如何修改模型配置？**
A: 编辑 .env 文件中的 OPENAI_MODEL 和 EMBEDDING_MODEL 参数。

**Q: 数据存储在哪里？**
A: 向量数据存储在 `storage/` 目录下的 SQLite 数据库中。

**Q: 如何清空数据库重新开始？**
A: 删除 `storage/` 目录下的所有文件，重启应用即可。

**Q: 为什么数据库中 text 字段为空，embedding 字段为 null？**
A: 这是正常现象！ChromaDB 使用分层存储：文本内容存储在全文搜索表中，向量数据存储在专门的 embeddings 表中。详见 [存储架构分析报告](docs/chromadb-storage-analysis.md)。

**Q: 如何配置 MySQL 数据库连接？**
A: 在 `.env` 文件中配置 MySQL 相关参数，然后运行 `python test_mysql_connection.py` 测试连接。

**Q: MySQL 连接失败怎么办？**
A: 检查以下几点：1) MySQL 服务是否启动；2) 数据库是否存在；3) 用户名密码是否正确；4) 网络连接是否正常。

## 🔧 开发和调试

### 开发模式启动

```bash
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 诊断工具

项目提供了完整的诊断脚本集合，用于排查和解决常见问题：

```bash
# 基础数据库检查
python scripts/check_database.py

# 诊断HNSW错误（如查询失败）
python scripts/diagnose_hnsw_error.py

# 检查向量维度配置
python scripts/check_embedding_dimensions.py

# 深入分析存储结构
python scripts/check_chromadb_storage.py
```

详细使用说明请参考 [诊断脚本指南](scripts/README.md)。

### 常见问题快速修复

**查询失败，出现 HNSW 错误**：

1. 运行 `python scripts/diagnose_hnsw_error.py`
2. 尝试重新加载文档（点击界面上的"重新加载文档"按钮）
3. 如果问题持续，运行 `python rebuild_database.py`

**向量维度不匹配**：

1. 运行 `python scripts/check_embedding_dimensions.py`
2. 根据输出建议修改配置或重建数据库

**MySQL 连接测试**：

1. 运行 `python test_mysql_connection.py` 测试 MySQL 连接
2. 检查 `.env` 文件中的 MySQL 配置
3. 确认 MySQL 服务正在运行

### 查看日志

应用会在控制台输出详细的调试信息，包括：

- 文档加载进度
- 检索过程
- API 请求响应
- 错误信息

### 性能优化

1. 调整 chunk_size 和 chunk_overlap 参数
2. 使用更高效的嵌入模型
3. 定期清理无用的向量数据
4. 监控 storage 目录大小
5. 定期运行诊断脚本检查系统健康状态

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 Issue 或联系开发者。
