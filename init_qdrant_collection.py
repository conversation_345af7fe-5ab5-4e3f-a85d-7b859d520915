#!/usr/bin/env python3
"""
初始化Qdrant集合，支持混合检索
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

import qdrant_client
from qdrant_client import models
from backend.config.settings import settings

def create_hybrid_collection():
    """创建支持混合检索的Qdrant集合"""
    try:
        print("🔧 连接Qdrant服务器...")
        client = qdrant_client.QdrantClient(
            host=settings.qdrant_host,
            port=settings.qdrant_port,
            api_key=settings.qdrant_api_key if settings.qdrant_api_key else None,
            prefer_grpc=settings.qdrant_prefer_grpc
        )
        
        collection_name = settings.collection_name
        
        # 检查集合是否已存在
        try:
            existing_collection = client.get_collection(collection_name)
            print(f"📋 集合 '{collection_name}' 已存在")
            print("✅ 使用现有集合进行混合检索")
            return True
        except Exception:
            print(f"📋 集合 '{collection_name}' 不存在，将创建新集合")
        
        # 创建新的混合检索集合
        print("🚀 创建混合检索集合...")
        client.create_collection(
            collection_name=collection_name,
            vectors_config={
                "text-dense": models.VectorParams(
                    size=1536,  # OpenAI text-embedding-3-small 的向量维度
                    distance=models.Distance.COSINE,
                )
            },
            sparse_vectors_config={
                "text-sparse": models.SparseVectorParams(
                    index=models.SparseIndexParams()
                )
            },
        )
        
        print(f"✅ 混合检索集合 '{collection_name}' 创建成功")
        
        # 验证集合配置
        collection_info = client.get_collection(collection_name)
        print(f"📊 集合信息:")
        print(f"  - 集合名称: {collection_name}")
        print(f"  - 点数: {collection_info.points_count}")
        print("  - 混合检索配置: 密集向量 + 稀疏向量")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建集合失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 初始化Qdrant混合检索集合")
    print("=" * 50)
    
    success = create_hybrid_collection()
    
    if success:
        print("\n🎉 Qdrant集合初始化成功！")
        print("📋 现在可以运行测试:")
        print("  python quick_test_hybrid.py")
        print("  python test_qdrant_hybrid_migration.py")
    else:
        print("\n💥 Qdrant集合初始化失败")
    
    sys.exit(0 if success else 1)
